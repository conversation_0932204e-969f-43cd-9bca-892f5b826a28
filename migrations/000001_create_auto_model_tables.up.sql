-- Create auto_model_configs table to store logical auto model configurations
CREATE TABLE IF NOT EXISTS `auto_model_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'Logical auto model name (e.g., automodel-1, automodel-2)',
  `description` varchar(255) DEFAULT NULL COMMENT 'Description of this auto model configuration',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Whether this configuration is active',
  `created_at` bigint(20) DEFAULT NULL COMMENT 'Creation timestamp',
  `updated_at` bigint(20) DEFAULT NULL COMMENT 'Last update timestamp',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_auto_model_configs_name` (`name`),
  KEY `idx_auto_model_configs_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stores logical auto model configurations';

-- Create auto_model_mappings table to store mappings between logical auto models and actual models
CREATE TABLE IF NOT EXISTS `auto_model_mappings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `config_id` bigint(20) unsigned NOT NULL COMMENT 'Reference to auto_model_configs.id',
  `model_name` varchar(255) NOT NULL COMMENT 'Actual model name that can be used in the system',
  `priority` int(11) NOT NULL DEFAULT '0' COMMENT 'Priority for model selection (higher value means higher priority)',
  `created_at` bigint(20) DEFAULT NULL COMMENT 'Creation timestamp',
  PRIMARY KEY (`id`),
  KEY `idx_auto_model_mappings_config` (`config_id`),
  KEY `idx_auto_model_mappings_priority` (`priority`),
  CONSTRAINT `fk_auto_model_mappings_config` FOREIGN KEY (`config_id`) REFERENCES `auto_model_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Maps logical auto models to actual model names';
