-- Add user_id column to auto_model_configs table
ALTER TABLE `auto_model_configs` 
ADD COLUMN `user_id` INT UNSIGNED DEFAULT NULL COMMENT 'User ID who owns this configuration' AFTER `id`,
ADD INDEX `idx_auto_model_configs_user` (`user_id`),
DROP INDEX `idx_auto_model_configs_name`,
ADD UNIQUE INDEX `idx_auto_model_configs_user_name` (`user_id`, `name`);

-- Update existing records to set user_id to 1 (or another default user)
-- This assumes there's a default admin user with ID 1
-- You may need to adjust this based on your actual user setup
UPDATE `auto_model_configs` SET `user_id` = 1 WHERE `user_id` IS NULL;
