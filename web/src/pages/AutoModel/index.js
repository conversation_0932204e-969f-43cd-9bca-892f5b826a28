import React, { useState, useEffect } from 'react';
import { Button, Table, Space, Modal, Form, Input, Switch, Tag, notification } from '@douyinfe/semi-ui';
import { IconPlus, IconEdit, IconDelete } from '@douyinfe/semi-icons';
import { API } from '../../helpers/api';

const AutoModelPage = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingId, setEditingId] = useState(null);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Models',
      dataIndex: 'models',
      key: 'models',
      render: (models) => (
        <Space wrap>
          {models.map((model, index) => (
            <Tag key={index} color="blue">
              {model}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'status',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>{isActive ? 'Active' : 'Inactive'}</Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            icon={<IconEdit />}
            onClick={() => handleEdit(record)}
            type="tertiary"
          />
          <Button
            icon={<IconDelete />}
            onClick={() => handleDelete(record.name)}
            type="danger"
          />
        </Space>
      ),
    },
  ];

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await API.get('/api/auto_models');
      if (response.data.success) {
        setData(response.data.data || []);
      } else {
        message.error(response.data.message || 'Failed to fetch auto models');
      }
    } catch (error) {
      console.error('Error fetching auto models:', error);
      message.error('Failed to fetch auto models');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleSubmit = async (values) => {
    try {
      setConfirmLoading(true);
      const url = editingId 
        ? `/api/auto_models/${editingId}`
        : '/api/auto_models';
      
      const method = editingId ? 'PUT' : 'POST';
      
      const response = await API({
        method,
        url,
        data: values
      });

      if (response.data.success) {
        message.success(
          editingId 
            ? 'Auto model updated successfully'
            : 'Auto model created successfully'
        );
        setVisible(false);
        fetchData();
      } else {
        message.error(response.data.message || 'Operation failed');
      }
    } catch (error) {
      console.error('Error saving auto model:', error);
      message.error('Failed to save auto model');
    } finally {
      setConfirmLoading(false);
    }
  };

  const handleEdit = (record) => {
    form.setFieldsValue({
      name: record.name,
      description: record.description || '',
      is_active: record.is_active,
      models: record.models || []
    });
    setEditingId(record.name);
    setVisible(true);
  };

  const handleDelete = (name) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: `Are you sure you want to delete "${name}"?`,
      onOk: async () => {
        try {
          const response = await API.delete(`/api/auto_models/${name}`);
          if (response.data.success) {
            message.success('Auto model deleted successfully');
            fetchData();
          } else {
            message.error(response.data.message || 'Failed to delete auto model');
          }
        } catch (error) {
          console.error('Error deleting auto model:', error);
          message.error('Failed to delete auto model');
        }
      },
    });
  };

  const handleAdd = () => {
    form.resetFields();
    setEditingId(null);
    setVisible(true);
  };

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <h2>Auto Model Management</h2>
        <Button 
          icon={<IconPlus />} 
          onClick={handleAdd}
          type="primary"
        >
          Add Auto Model
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="name"
        pagination={{
          pageSize: 10,
        }}
      />

      <Modal
        title={`${editingId ? 'Edit' : 'Add'} Auto Model`}
        visible={visible}
        onOk={form.submit}
        onCancel={() => setVisible(false)}
        confirmLoading={confirmLoading}
        width={600}
      >
        <Form
          form={form}
          onSubmit={handleSubmit}
          labelPosition="left"
          labelWidth={120}
          style={{ marginTop: '20px' }}
        >
          {!editingId && (
            <Form.Input
              field="name"
              label="Name"
              rules={[{ required: true, message: 'Please enter a name' }]}
              style={{ width: '100%' }}
            />
          )}
          <Form.Input
            field="description"
            label="Description"
            style={{ width: '100%' }}
          />
          <Form.Switch
            field="is_active"
            label="Active"
            initValue={true}
          />
          <Form.TextArea
            field="models"
            label="Models (one per line)"
            placeholder="Enter one model per line"
            style={{ width: '100%', minHeight: '100px' }}
            rules={[{ required: true, message: 'Please enter at least one model' }]}
            render={(props) => (
              <Input.TextArea
                {...props}
                value={Array.isArray(props.value) ? props.value.join('\n') : ''}
                onChange={(value, e) => {
                  const models = value
                    .split('\n')
                    .map((m) => m.trim())
                    .filter((m) => m);
                  props.onChange(models);
                }}
              />
            )}
          />
        </Form>
      </Modal>
    </div>
  );
};

export default AutoModelPage;
