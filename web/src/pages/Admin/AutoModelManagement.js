import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  Select,
  Space,
  Card,
  Empty,
  Popconfirm
} from '@douyinfe/semi-ui';
import {
  IconPlus,
  IconEdit,
  IconDelete
} from '@douyinfe/semi-icons';
import { API, showError, showSuccess } from '../../helpers';

export default function AdminAutoModelManagement() {
  const [configs, setConfigs] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [formApi, setFormApi] = useState(null);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load users
      const usersRes = await API.get('/api/user/');
      if (usersRes.data.success) {
        setUsers(usersRes.data.data);
      } else {
        showError(usersRes.data.message || 'Failed to load users');
      }
      
      // Load configs
      const configsRes = await API.get('/api/admin/auto_models');
      if (configsRes.data.success) {
        setConfigs(configsRes.data.data || []);
      } else {
        showError(configsRes.data.message || 'Failed to load configurations');
      }
    } catch (error) {
      showError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleSubmit = async (values) => {
    const config = {
      ...values,
      models: values.models.split('\n').filter(m => m.trim() !== '')
    };

    try {
      if (editingConfig) {
        await API.put(`/api/admin/auto_models/${editingConfig.name}?user_id=${editingConfig.user_id}`, config);
        showSuccess('Updated successfully');
      } else {
        await API.post('/api/admin/auto_models', config);
        showSuccess('Created successfully');
      }
      setModalVisible(false);
      formApi?.reset();
      loadData();
    } catch (error) {
      showError(error.message);
    }
  };

  const handleDelete = async (config) => {
    try {
      await API.delete(`/api/admin/auto_models/${config.name}?user_id=${config.user_id}`);
      showSuccess('Deleted successfully');
      loadData();
    } catch (error) {
      showError(error.message);
    }
  };

  const columns = [
    {
      title: 'User',
      dataIndex: 'user',
      key: 'username',
      render: (user) => user ? user.username : 'Unknown'
    },
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Active', dataIndex: 'is_active', key: 'is_active', render: (val) => val ? 'Yes' : 'No' },
    { title: 'Models', dataIndex: 'models', key: 'models', render: models => models && models.length > 0 ? models.join(', ') : 'No models configured' },
    {
      title: 'Actions',
      key: 'actions',
      render: (text, record) => (
        <Space>
          <Button
            icon={<IconEdit />}
            size="small"
            onClick={() => {
              setEditingConfig(record);
              formApi?.setValues({
                ...record,
                models: record.models && record.models.length > 0 ? record.models.join('\n') : ''
              });
              setModalVisible(true);
            }}
          >
            Edit
          </Button>
          <Popconfirm
            title="Delete this config?"
            onConfirm={() => handleDelete(record)}
            content="Are you sure you want to delete this auto model configuration?"
          >
            <Button type="danger" icon={<IconDelete />} size="small">Delete</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="mt-[64px] px-2">
      <Card
        className="!rounded-2xl"
        title={
          <div className="flex justify-between items-center">
            <span>Auto Model Management</span>
            <Button
              type="primary"
              icon={<IconPlus />}
              onClick={() => {
                setEditingConfig(null);
                formApi?.reset();
                formApi?.setValue('is_active', true);
                setModalVisible(true);
              }}
            >
              Add Configuration
            </Button>
          </div>
        }
        shadows="always"
        bordered={false}
      >
        <Table
          columns={columns}
          dataSource={configs}
          rowKey={record => `${record.user_id}-${record.name}`}
          loading={loading}
          empty={
            <Empty
              description="No auto model configurations found"
              style={{ padding: 30 }}
            />
          }
          className="rounded-xl overflow-hidden"
          size="middle"
        />
      </Card>

      <Modal
        title={`${editingConfig ? 'Edit' : 'Add'} Configuration`}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => formApi?.submitForm()}
        width={600}
        okText="Save"
        cancelText="Cancel"
      >
        <Form
          getFormApi={(api) => setFormApi(api)}
          onSubmit={handleSubmit}
          labelPosition="top"
        >
          <Form.Select
            field="user_id"
            label="User"
            rules={[{ required: true, message: 'Please select a user' }]}
            optionList={users.map(u => ({ value: u.id, label: u.username }))}
            placeholder="Select a user"
          />
          <Form.Input
            field="name"
            label="Name"
            rules={[{ required: true, message: 'Please enter a name' }]}
            disabled={!!editingConfig}
            placeholder="Enter configuration name"
          />
          <Form.TextArea
            field="description"
            label="Description"
            placeholder="Enter description (optional)"
          />
          <Form.Switch
            field="is_active"
            label="Active"
          />
          <Form.TextArea
            field="models"
            label="Models (one per line)"
            rules={[{ required: true, message: 'Please enter at least one model' }]}
            rows={8}
            placeholder="Enter model names, one per line"
          />
        </Form>
      </Modal>
    </div>
  );
}
