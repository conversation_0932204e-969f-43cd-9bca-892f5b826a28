import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  Space,
  Empty,
  Popconfirm
} from '@douyinfe/semi-ui';
import {
  IconPlus,
  IconEdit,
  IconDelete
} from '@douyinfe/semi-icons';
import { API, showError, showSuccess } from '../../../helpers';

export default function UserAutoModelManagement() {
  const [configs, setConfigs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [formApi, setFormApi] = useState(null);

  const loadConfigs = async () => {
    setLoading(true);
    try {
      const res = await API.get('/api/auto_models');
      if (res.data.success) {
        setConfigs(res.data.data);
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError('Failed to load auto model configurations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  const handleSubmit = async (values) => {
    const config = {
      name: values.name,
      description: values.description,
      is_active: values.is_active,
      models: values.models.split('\n').filter(m => m.trim() !== '')
    };

    try {
      if (editingConfig) {
        // Update existing config
        await API.put(`/api/auto_models/${editingConfig.name}`, config);
        showSuccess('Configuration updated successfully');
      } else {
        // Create new config
        await API.post('/api/auto_models', config);
        showSuccess('Configuration created successfully');
      }
      setModalVisible(false);
      loadConfigs();
    } catch (error) {
      showError(error.message);
    }
  };

  const handleDelete = async (name) => {
    try {
      await API.delete(`/api/auto_models/${name}`);
      showSuccess('Configuration deleted successfully');
      loadConfigs();
    } catch (error) {
      showError(error.message);
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Active',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (isActive ? 'Yes' : 'No'),
    },
    {
      title: 'Models',
      dataIndex: 'models',
      key: 'models',
      render: (models) => models && models.length > 0 ? models.join(', ') : 'No models configured',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Button
            icon={<EditOutlined />}
            onClick={() => {
              setEditingConfig(record);
              form.setFieldsValue({
                ...record,
                models: record.models && record.models.length > 0 ? record.models.join('\n') : '',
              });
              setModalVisible(true);
            }}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure you want to delete this configuration?"
            onConfirm={() => handleDelete(record.name)}
            okText="Yes"
            cancelText="No"
          >
            <Button danger icon={<DeleteOutlined />}>Delete</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingConfig(null);
            form.resetFields();
            setModalVisible(true);
          }}
        >
          Add Configuration
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={configs}
        rowKey="name"
        loading={loading}
      />

      <Modal
        title={editingConfig ? 'Edit Configuration' : 'Add Configuration'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ is_active: true }}
        >
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please input a name!' }]}
          >
            <Input disabled={!!editingConfig} />
          </Form.Item>
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={2} />
          </Form.Item>
          <Form.Item
            name="is_active"
            label="Active"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          <Form.Item
            name="models"
            label="Models (one per line)"
            rules={[{ required: true, message: 'Please input at least one model!' }]}
          >
            <Input.TextArea rows={8} placeholder="Enter one model per line" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
