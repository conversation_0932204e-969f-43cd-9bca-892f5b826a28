import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  Space,
  Empty,
  Popconfirm
} from '@douyinfe/semi-ui';
import {
  IconPlus,
  IconEdit,
  IconDelete
} from '@douyinfe/semi-icons';
import { API, showError, showSuccess } from '../../../helpers';

export default function AutoModelManagement() {
  const [configs, setConfigs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [formApi, setFormApi] = useState(null);

  const loadConfigs = async () => {
    setLoading(true);
    try {
      const res = await API.get('/api/auto_models');
      if (res.data.success) {
        setConfigs(res.data.data);
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError('Failed to load auto model configurations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  const handleSubmit = async (values) => {
    const config = {
      name: values.name,
      description: values.description,
      is_active: values.is_active,
      models: values.models.split('\n').filter(m => m.trim() !== '')
    };

    try {
      if (editingConfig) {
        // Update existing config
        await API.put(`/api/auto_models/${editingConfig.name}`, config);
        showSuccess('Configuration updated successfully');
      } else {
        // Create new config
        await API.post('/api/auto_models', config);
        showSuccess('Configuration created successfully');
      }
      setModalVisible(false);
      loadConfigs();
    } catch (error) {
      showError('Failed to save configuration');
    }
  };

  const handleDelete = async (name) => {
    try {
      await API.delete(`/api/auto_models/${name}`);
      showSuccess('Configuration deleted successfully');
      loadConfigs();
    } catch (error) {
      showError('Failed to delete configuration');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <span style={{ color: isActive ? '#52c41a' : '#ff4d4f' }}>
          {isActive ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      title: 'Models',
      dataIndex: 'models',
      key: 'models',
      render: (models) => models && models.length > 0 ? models.join(', ') : 'No models configured',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Button
            icon={<IconEdit />}
            onClick={() => {
              setEditingConfig(record);
              formApi?.setValues({
                ...record,
                models: record.models && record.models.length > 0 ? record.models.join('\n') : ''
              });
              setModalVisible(true);
            }}
          />
          <Popconfirm
            title="Are you sure to delete this configuration?"
            onConfirm={() => handleDelete(record.name)}
            okText="Yes"
            cancelText="No"
          >
            <Button theme="borderless" type="danger" icon={<IconDelete />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="Auto Model Management"
        extra={
          <Button
            type="primary"
            icon={<IconPlus />}
            onClick={() => {
              setEditingConfig(null);
              formApi?.reset();
              setModalVisible(true);
            }}
          >
            Add Configuration
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={configs}
          rowKey="name"
          loading={loading}
          pagination={false}
          empty={
            <Empty
              description="No auto model configurations found"
              style={{ padding: 30 }}
            />
          }
        />
      </Card>

      <Modal
        title={editingConfig ? 'Edit Configuration' : 'Add Configuration'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => formApi?.submitForm()}
        width={700}
      >
        <Form
          getFormApi={(formApi) => setFormApi(formApi)}
          layout="vertical"
          onSubmit={handleSubmit}
          initValues={{ is_active: true }}
        >
          <Form.Input
            field="name"
            label="Name"
            rules={[{ required: true, message: 'Please input a name' }]}
            disabled={!!editingConfig}
          />

          <Form.TextArea
            field="description"
            label="Description"
          />

          <Form.Switch
            field="is_active"
            label="Status"
            checkedText="Active"
            uncheckedText="Inactive"
          />

          <Form.TextArea
            field="models"
            label="Models (one per line)"
            rules={[{ required: true, message: 'Please input at least one model' }]}
            rows={5}
            placeholder="model1
model2
model3"
          />
        </Form>
      </Modal>
    </div>
  );
}
