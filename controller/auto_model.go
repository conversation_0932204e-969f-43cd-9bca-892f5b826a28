package controller

import (
	"fmt"
	"net/http"
	"one-api/model"

	"github.com/gin-gonic/gin"
)

// ListAutoModels GET /api/auto_models
func ListAutoModels(c *gin.Context) {
	userID := c.GetInt("id") // Get user ID from context
	autoModels, err := model.GetAllAutoModelConfigs(model.DB, uint(userID))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    autoModels,
	})
}

// UpdateAutoModels POST /api/auto_models
func UpdateAutoModels(c *gin.Context) {
	var request struct {
		Models []string `json:"models"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request format",
		})
		return
	}

	if len(request.Models) == 0 {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "At least one model is required",
		})
		return
	}

	// Update models in database
	// This function needs to be implemented or replaced with appropriate logic
	// For now, return an error indicating this functionality is not available
	err := fmt.Errorf("UpdateAutoModels function not implemented")
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Auto models updated successfully",
	})
}
